#!/usr/bin/env node

/**
 * Test script for Azure OpenAI with Portkey integration
 */

require('dotenv').config();
const axios = require('axios');

async function testAzureOpenAIPortkey() {
    console.log('=== Azure OpenAI Portkey Test ===');
    
    // Get environment variables
    const portkeyBaseUrl = process.env.PORTKEY_BASE_URL || 'http://**************:8787';
    const openaiApiKey = process.env.OPENAI_API_KEY;
    const azureEndpoint = process.env.AZURE_OPENAI_ENDPOINT;
    const azureApiVersion = process.env.AZURE_OPENAI_API_VERSION;
    
    console.log(`Portkey Base URL: ${portkeyBaseUrl}`);
    console.log(`Azure Endpoint: ${azureEndpoint}`);
    console.log(`Azure API Version: ${azureApiVersion}`);
    console.log(`OpenAI API Key: ${openaiApiKey ? '*'.repeat(20) : 'NOT SET'}`);
    console.log('');
    
    if (!portkeyBaseUrl || !openaiApiKey || !azureEndpoint || !azureApiVersion) {
        console.log('❌ Missing required environment variables!');
        return false;
    }
    
    // Extract resource name from Azure endpoint
    let resourceName;
    try {
        resourceName = azureEndpoint.split('//')[1].split('.')[0];
        console.log(`Extracted Azure Resource Name: ${resourceName}`);
    } catch (error) {
        console.log('❌ Could not extract resource name from Azure endpoint');
        return false;
    }
    
    // Prepare the request
    const url = `${portkeyBaseUrl}/v1/chat/completions`;
    
    const headers = {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${openaiApiKey}`,
        'x-portkey-provider': 'azure-openai',
        'x-portkey-azure-resource-name': resourceName,
        'x-portkey-azure-deployment-id': 'gpt-4o-mini', // You may need to adjust this
        'x-portkey-azure-api-version': azureApiVersion,
        'x-portkey-azure-model-name': 'gpt-4o-mini'
    };
    
    const data = {
        messages: [
            { role: 'user', content: 'Hello, how are you? Please respond with a short greeting.' }
        ],
        model: 'gpt-4o-mini',
        max_tokens: 100,
        temperature: 0.7
    };
    
    console.log('=== Request Details ===');
    console.log(`URL: ${url}`);
    console.log('Headers:');
    Object.entries(headers).forEach(([key, value]) => {
        if (key.toLowerCase().includes('authorization') || key.toLowerCase().includes('key')) {
            console.log(`  ${key}: ${'*'.repeat(20)}`);
        } else {
            console.log(`  ${key}: ${value}`);
        }
    });
    console.log(`Data: ${JSON.stringify(data, null, 2)}`);
    console.log('');
    
    try {
        console.log('🔄 Sending request...');
        const response = await axios.post(url, data, { 
            headers,
            timeout: 30000,
            validateStatus: () => true // Don't throw on non-2xx status codes
        });
        
        console.log(`Status Code: ${response.status}`);
        console.log(`Response Headers: ${JSON.stringify(response.headers, null, 2)}`);
        console.log('');
        
        if (response.status === 200) {
            console.log('✅ Success! Response:');
            console.log(JSON.stringify(response.data, null, 2));
            
            // Extract the actual message
            if (response.data.choices && response.data.choices.length > 0) {
                const message = response.data.choices[0].message.content;
                console.log(`\n🤖 AI Response: ${message}`);
            }
            
            return true;
        } else {
            console.log(`❌ Error ${response.status}:`);
            console.log(JSON.stringify(response.data, null, 2));
            return false;
        }
        
    } catch (error) {
        if (error.response) {
            console.log(`❌ Request failed with status ${error.response.status}:`);
            console.log(JSON.stringify(error.response.data, null, 2));
        } else if (error.request) {
            console.log('❌ Request failed - no response received:');
            console.log(error.message);
        } else {
            console.log(`❌ Unexpected error: ${error.message}`);
        }
        return false;
    }
}

async function testDirectAzureOpenAI() {
    console.log('\n=== Direct Azure OpenAI Test ===');
    
    const openaiApiKey = process.env.OPENAI_API_KEY;
    const azureEndpoint = process.env.AZURE_OPENAI_ENDPOINT;
    const azureApiVersion = process.env.AZURE_OPENAI_API_VERSION;
    
    if (!openaiApiKey || !azureEndpoint || !azureApiVersion) {
        console.log('❌ Missing required Azure OpenAI environment variables!');
        return false;
    }
    
    // Direct Azure OpenAI endpoint
    const url = `${azureEndpoint}openai/deployments/gpt-4o-mini/chat/completions?api-version=${azureApiVersion}`;
    
    const headers = {
        'Content-Type': 'application/json',
        'api-key': openaiApiKey
    };
    
    const data = {
        messages: [
            { role: 'user', content: 'Hello, this is a direct Azure OpenAI test.' }
        ],
        max_tokens: 100,
        temperature: 0.7
    };
    
    try {
        console.log('🔄 Testing direct Azure OpenAI connection...');
        const response = await axios.post(url, data, { 
            headers,
            timeout: 30000,
            validateStatus: () => true
        });
        
        console.log(`Status Code: ${response.status}`);
        
        if (response.status === 200) {
            console.log('✅ Direct Azure OpenAI connection successful!');
            if (response.data.choices && response.data.choices.length > 0) {
                const message = response.data.choices[0].message.content;
                console.log(`🤖 AI Response: ${message}`);
            }
            return true;
        } else {
            console.log(`❌ Direct Azure OpenAI failed with status ${response.status}:`);
            console.log(JSON.stringify(response.data, null, 2));
            return false;
        }
        
    } catch (error) {
        if (error.response) {
            console.log(`❌ Direct Azure OpenAI test failed with status ${error.response.status}:`);
            console.log(JSON.stringify(error.response.data, null, 2));
        } else {
            console.log(`❌ Direct Azure OpenAI test failed: ${error.message}`);
        }
        return false;
    }
}

async function testPortkeySDK() {
    console.log('\n=== Portkey SDK Test ===');
    
    try {
        const Portkey = require('portkey-ai');
        
        const portkey = new Portkey({
            apiKey: process.env.OPENAI_API_KEY,
            baseURL: process.env.PORTKEY_BASE_URL || 'http://**************:8787',
            provider: 'azure-openai',
            azureResourceName: process.env.AZURE_OPENAI_ENDPOINT?.split('//')[1]?.split('.')[0],
            azureDeploymentId: 'gpt-4o-mini',
            azureApiVersion: process.env.AZURE_OPENAI_API_VERSION,
            azureModelName: 'gpt-4o-mini'
        });
        
        console.log('🔄 Testing with Portkey SDK...');
        
        const completion = await portkey.chat.completions.create({
            messages: [
                { role: 'user', content: 'Hello from Portkey SDK! Please respond briefly.' }
            ],
            model: 'gpt-4o-mini',
            max_tokens: 100
        });
        
        console.log('✅ Portkey SDK test successful!');
        console.log(`🤖 AI Response: ${completion.choices[0].message.content}`);
        return true;
        
    } catch (error) {
        console.log(`❌ Portkey SDK test failed: ${error.message}`);
        if (error.response) {
            console.log('Response data:', JSON.stringify(error.response.data, null, 2));
        }
        return false;
    }
}

async function main() {
    console.log('Testing Azure OpenAI with Portkey Gateway\n');
    
    // Test direct Azure OpenAI first
    const directSuccess = await testDirectAzureOpenAI();
    
    // Test through Portkey with raw HTTP
    const portkeySuccess = await testAzureOpenAIPortkey();
    
    // Test with Portkey SDK
    const sdkSuccess = await testPortkeySDK();
    
    console.log('\n=== Summary ===');
    console.log(`Direct Azure OpenAI: ${directSuccess ? '✅ Success' : '❌ Failed'}`);
    console.log(`Portkey Gateway (HTTP): ${portkeySuccess ? '✅ Success' : '❌ Failed'}`);
    console.log(`Portkey SDK: ${sdkSuccess ? '✅ Success' : '❌ Failed'}`);
    
    if (!directSuccess) {
        console.log('\n💡 Recommendation: Fix direct Azure OpenAI connection first');
        console.log('   - Check your Azure OpenAI endpoint and API key');
        console.log('   - Verify the deployment name (gpt-4o-mini) exists in your Azure OpenAI resource');
    } else if (!portkeySuccess && !sdkSuccess) {
        console.log('\n💡 Recommendation: Check Portkey configuration');
        console.log('   - Verify the Portkey base URL is accessible');
        console.log('   - Check deployment names and resource configuration');
    }
}

if (require.main === module) {
    main().catch(console.error);
}

module.exports = { testAzureOpenAIPortkey, testDirectAzureOpenAI, testPortkeySDK };
