require('dotenv').config();

/**
 * Portkey Configuration Module
 * 
 * This module provides configuration for Portkey AI Gateway integration
 * with LangSmith tracing and monitoring capabilities.
 */

const portkeyConfig = {
  // Portkey API Configuration
  apiKey: process.env.PORTKEY_API_KEY || '',
  baseURL: process.env.PORTKEY_BASE_URL || 'https://api.portkey.ai/v1',
  
  // Virtual Keys for different providers
  virtualKeys: {
    openai: process.env.PORTKEY_VIRTUAL_KEY_OPENAI || '',
    azure: process.env.PORTKEY_VIRTUAL_KEY_AZURE || '',
    anthropic: process.env.PORTKEY_VIRTUAL_KEY_ANTHROPIC || '',
  },
  
  // LangSmith Configuration
  langsmith: {
    apiKey: process.env.LANGSMITH_API_KEY || '',
    projectName: process.env.LANGSMITH_PROJECT || 'SellerBot',
    endpoint: process.env.LANGSMITH_ENDPOINT || 'https://api.smith.langchain.com',
    tracingEnabled: process.env.LANGSMITH_TRACING === 'true' || true,
  },
  
  // Default Portkey Configuration
  defaultConfig: {
    provider: 'openai',
    retry: {
      attempts: 3,
      onStatusCodes: [429, 500, 502, 503, 504],
    },
    cache: {
      mode: 'semantic',
      maxAge: 3600, // 1 hour in seconds
    },
    requestTimeout: 30000, // 30 seconds
    metadata: {
      environment: process.env.NODE_ENV || 'development',
      service: 'SellerBot',
      version: '1.0.0',
    },
  },
  
  // Model configurations for different use cases
  modelConfigs: {
    // For general chat completions
    chat: {
      provider: 'openai',
      model: 'gpt-4o',
      temperature: 0.7,
      maxTokens: 1000,
    },

    // For assistant API calls
    assistant: {
      provider: 'openai',
      model: 'gpt-4o',
      temperature: 0.3,
      maxTokens: 2000,
    },

    // For Azure OpenAI
    azure: {
      provider: 'azure-openai',
      model: process.env.OPENAI_MODEL_ID || 'gpt-4o',
      deployment: process.env.AZURE_OPENAI_DEPLOYMENT || process.env.OPENAI_MODEL_ID || 'gpt-4o',
      endpoint: process.env.AZURE_OPENAI_ENDPOINT,
      apiVersion: process.env.AZURE_OPENAI_API_VERSION,
      temperature: 0.7,
      maxTokens: 1000,
    },

    // For text processing and analysis
    analysis: {
      provider: 'openai',
      model: 'gpt-4o-mini',
      temperature: 0.1,
      maxTokens: 500,
    },
  },
  
  // Logging configuration
  logging: {
    enabled: process.env.PORTKEY_LOGGING === 'true' || true,
    level: process.env.PORTKEY_LOG_LEVEL || 'info',
    includeRequestBody: process.env.PORTKEY_LOG_REQUESTS === 'true' || false,
    includeResponseBody: process.env.PORTKEY_LOG_RESPONSES === 'true' || false,
  },
  
  // Rate limiting configuration
  rateLimiting: {
    enabled: process.env.PORTKEY_RATE_LIMITING === 'true' || true,
    requestsPerMinute: parseInt(process.env.PORTKEY_RPM_LIMIT) || 60,
    tokensPerMinute: parseInt(process.env.PORTKEY_TPM_LIMIT) || 100000,
  },
  
  // Cost tracking configuration
  costTracking: {
    enabled: process.env.PORTKEY_COST_TRACKING === 'true' || true,
    budgetLimit: parseFloat(process.env.PORTKEY_BUDGET_LIMIT) || 100.0, // USD
    alertThreshold: parseFloat(process.env.PORTKEY_ALERT_THRESHOLD) || 80.0, // Percentage
  },
  
  // Fallback configuration
  fallback: {
    enabled: process.env.PORTKEY_FALLBACK === 'true' || true,
    providers: ['openai', 'azure-openai'],
    strategy: 'round-robin', // or 'priority'
  },
};

/**
 * Get Portkey configuration for a specific use case
 * @param {string} useCase - The use case (chat, assistant, azure, analysis)
 * @returns {object} Configuration object for Portkey
 */
function getPortkeyConfig(useCase = 'chat') {
  const baseConfig = {
    apiKey: portkeyConfig.apiKey,
    baseURL: portkeyConfig.baseURL,
    ...portkeyConfig.defaultConfig,
  };

  const modelConfig = portkeyConfig.modelConfigs[useCase] || portkeyConfig.modelConfigs.chat;

  // Set virtual key based on provider
  if (modelConfig.provider === 'azure-openai') {
    baseConfig.virtualKey = portkeyConfig.virtualKeys.azure;

    // Add Azure-specific configuration
    if (modelConfig.endpoint) {
      baseConfig.azureResourceName = modelConfig.endpoint.split('//')[1]?.split('.')[0];
    }
    if (modelConfig.deployment) {
      baseConfig.azureDeploymentId = modelConfig.deployment;
      baseConfig.azureModelName = modelConfig.deployment;
    }
    if (modelConfig.apiVersion) {
      baseConfig.azureApiVersion = modelConfig.apiVersion;
    }
  } else if (modelConfig.provider === 'openai') {
    baseConfig.virtualKey = portkeyConfig.virtualKeys.openai;
  }

  return {
    ...baseConfig,
    ...modelConfig,
  };
}

/**
 * Get LangSmith configuration
 * @returns {object} LangSmith configuration
 */
function getLangSmithConfig() {
  return portkeyConfig.langsmith;
}

/**
 * Validate configuration
 * @returns {object} Validation result with status and missing keys
 */
function validateConfig() {
  const missing = [];
  const warnings = [];
  
  if (!portkeyConfig.apiKey) {
    missing.push('PORTKEY_API_KEY');
  }
  
  if (!portkeyConfig.virtualKeys.openai) {
    warnings.push('PORTKEY_VIRTUAL_KEY_OPENAI');
  }
  
  if (!portkeyConfig.langsmith.apiKey) {
    warnings.push('LANGSMITH_API_KEY');
  }
  
  return {
    isValid: missing.length === 0,
    missing,
    warnings,
    message: missing.length > 0 
      ? `Missing required environment variables: ${missing.join(', ')}`
      : warnings.length > 0
      ? `Optional environment variables not set: ${warnings.join(', ')}`
      : 'Configuration is valid',
  };
}

module.exports = {
  portkeyConfig,
  getPortkeyConfig,
  getLangSmithConfig,
  validateConfig,
};
